// src/app/api/fal/proxy/route.ts
// @ts-ignore
import { route as falServerProxyRoute } from '@fal-ai/server-proxy/nextjs';
import { NextRequest, NextResponse } from 'next/server';

// The POST handler is for submitting the job. 
// It should just use the official proxy handler directly.
// The initial response will be a queue status, not the final image.
export async function POST(request: NextRequest) {
    console.log('[FAL_PROXY_POST_REQUEST_RECEIVED] Target URL:', request.headers.get('x-fal-target-url'));
    const response = await falServerProxyRoute.POST(request);
    // Log the initial queue response for debugging if needed
    try {
        const responseBody = await response.clone().json();
        console.log('[FAL_PROXY_POST_RESPONSE_BODY]', JSON.stringify(responseBody, null, 2));
    } catch (e) {
        const responseText = await response.clone().text();
        console.log('[FAL_PROXY_POST_RESPONSE_TEXT]', responseText);
    }
    return response;
}

// The GET handler is used by @fal-ai/client to poll for status and final results.
// This is where we need to intercept and modify the image URL if the job is complete.
export async function GET(request: NextRequest) {
    console.log('[FAL_PROXY_GET_REQUEST_RECEIVED] URL:', request.url);
    const responseFromFalServer = await falServerProxyRoute.GET(request);

    console.log('[FAL_PROXY_GET_RAW_RESPONSE_STATUS]', responseFromFalServer.status, responseFromFalServer.statusText);

    if (!responseFromFalServer.ok) {
        try {
            const errorBody = await responseFromFalServer.clone().json();
            console.error('[FAL_PROXY_GET_RAW_ERROR_JSON_BODY]', JSON.stringify(errorBody, null, 2));
        } catch (e) {
            const errorText = await responseFromFalServer.clone().text();
            console.error('[FAL_PROXY_GET_RAW_ERROR_TEXT_BODY]', errorText);
        }
        return responseFromFalServer;
    }

    try {
        const clonedResponse = responseFromFalServer.clone();
        const falResultJson: any = await clonedResponse.json();

        console.log('[FAL_PROXY_GET_RAW_JSON_RESULT]', JSON.stringify(falResultJson, null, 2));

        // Check if this response contains the final image data
        // A common indicator for completion might be a specific status, or the presence of the 'images' field.
        // Fal.ai's successful image generation output schema includes an "images" array.
        if (falResultJson && falResultJson.images && Array.isArray(falResultJson.images) && falResultJson.images.length > 0) {
            console.log('[FAL_PROXY_GET] Detected final image result. Modifying image URLs.');
            falResultJson.images = falResultJson.images.map((image: any) => {
                if (image.url) {
                    const originalFalImageUrl = image.url;
                    const ourImageProxyUrl = `/api/image-proxy?sourceUrl=${encodeURIComponent(originalFalImageUrl)}`;
                    return {
                        ...image,
                        url: ourImageProxyUrl,
                        is_proxied: true,
                    };
                }
                return image;
            });
            return NextResponse.json(falResultJson);
        } else {
            // If it's not the final image result (e.g., still "IN_PROGRESS", "IN_QUEUE"),
            // return the original JSON from Fal.ai without modification.
            console.log('[FAL_PROXY_GET] Not final image result or no images found. Returning original Fal.ai JSON.');
            return NextResponse.json(falResultJson);
        }
    } catch (error) {
        console.error("Error processing Fal.ai GET response in proxy:", error);
        // If parsing or modification fails, it's safer to return the original response from Fal.ai
        // if it hasn't been consumed. Since we cloned it, we can return the original.
        // However, if .json() failed on clonedResponse, responseFromFalServer might also fail.
        // It might be better to return a custom error.
        // For now, let's try returning the original response if parsing falResultJson failed.
        // If an error occurred after parsing, this return won't be hit.
        // A more robust way would be to check if clonedResponse.json() itself threw.
        // The outer try/catch should handle errors during modification.
        return responseFromFalServer; // Fallback to original response if our processing fails
    }
}

// src/app/api/image-proxy/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
    const { searchParams } = new URL(request.url);
    const sourceUrl = searchParams.get('sourceUrl');

    if (!sourceUrl) {
        return NextResponse.json({ error: 'Missing sourceUrl parameter' }, { status: 400 });
    }

    try {
        const decodedSourceUrl = decodeURIComponent(sourceUrl);

        // Validate if the decodedSourceUrl is a valid Fal.ai media URL (optional but recommended for security)
        // Example check: if (!decodedSourceUrl.startsWith('https://fal.media/')) {
        //    return NextResponse.json({ error: 'Invalid source URL' }, { status: 400 });
        // }

        const imageResponseFromFal = await fetch(decodedSourceUrl, {
            headers: {
                // It's good practice to forward some headers if necessary,
                // but for simple image fetching, usually not required.
                // 'Authorization': `Bearer YOUR_FAL_KEY_IF_NEEDED_FOR_DIRECT_CDN_ACCESS` - Fal CDN URLs are typically public.
            }
        });

        if (!imageResponseFromFal.ok) {
            // Log the error from Fal.ai for debugging
            const errorBody = await imageResponseFromFal.text();
            console.error(`Failed to fetch image from Fal.ai. Status: ${imageResponseFromFal.status}, URL: ${decodedSourceUrl}, Body: ${errorBody}`);
            return NextResponse.json(
                { error: 'Failed to fetch image from original source', status: imageResponseFromFal.status },
                { status: imageResponseFromFal.status }
            );
        }

        const imageBuffer = await imageResponseFromFal.arrayBuffer();
        const contentType = imageResponseFromFal.headers.get('content-type') || 'application/octet-stream';

        // Return the image data directly
        return new NextResponse(imageBuffer, {
            status: 200,
            headers: {
                'Content-Type': contentType,
                'Cache-Control': 'public, max-age=3600, immutable', // Cache for 1 hour, immutable
            },
        });

    } catch (error: any) {
        console.error('Custom image proxy error:', error);
        return NextResponse.json({ error: 'Failed to proxy image', details: error.message }, { status: 500 });
    }
}

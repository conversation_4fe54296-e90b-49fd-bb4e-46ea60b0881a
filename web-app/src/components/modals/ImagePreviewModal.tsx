"use client";

import { HistoryItem } from "@/components/sections/HistorySection"; // Assuming HistoryItem is exported here
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
// Import for next/image is removed as we are using <img> for now.
// If we revert, this needs to be added back: import Image from "next/image";


interface ImagePreviewModalProps {
    isOpen: boolean;
    onClose: () => void;
    item: HistoryItem | null;
}

// Placeholder Icons
const DownloadIcon = () => <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>;
const XIcon = () => <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>;

// getAspectRatioClass function is removed as per the new strategy
// const getAspectRatioClass = (aspectRatioValue?: string): string => { ... };

export const ImagePreviewModal = ({ isOpen, onClose, item }: ImagePreviewModalProps) => {
    console.log("ImagePreviewModal item:", item);
    if (!item) return null;

    // imageContainerAspectRatioClass is no longer used
    // const imageContainerAspectRatioClass = getAspectRatioClass(item.aspectRatioValue);

    const handleDownload = () => {
        const link = document.createElement('a');
        link.href = item.imageUrl;
        const promptPart = item.prompt ? item.prompt.substring(0, 30).replace(/\s+/g, '_') : 'image';
        const timestampPart = new Date(item.timestamp).toISOString().split('T')[0];
        link.download = `LuckyX-AI-${promptPart}-${timestampPart}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[85vw] md:max-w-[75vw] lg:max-w-[65vw] xl:max-w-[55vw] max-h-[90vh] flex flex-col bg-card border-border p-0">
                <DialogHeader className="flex-shrink-0 p-4 border-b border-border">
                    <DialogTitle className="text-sm font-normal text-foreground whitespace-normal max-h-[12vh] overflow-y-auto pr-8">
                        {item.prompt || "图像详情"}
                    </DialogTitle>
                    {/* Default Shadcn UI DialogClose should handle closing. It's part of DialogContent. */}
                </DialogHeader>

                <div className="flex-grow relative flex justify-center items-center overflow-hidden p-2 sm:p-4 group">
                    {/* Using standard img tag for simpler scaling behavior controlled by CSS */}
                    <img
                        src={item.imageUrl}
                        alt={item.prompt || "Generated image preview"}
                        className="object-contain max-w-full max-h-full w-auto h-auto rounded"
                        style={{ display: 'block' }} // Ensures img behaves well
                    />
                    <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute bottom-3 right-3 sm:bottom-5 sm:right-5 h-9 w-9 bg-black/40 hover:bg-black/60 text-white rounded-full opacity-60 group-hover:opacity-100 transition-opacity z-10"
                        onClick={handleDownload}
                        aria-label="Download image"
                    >
                        <DownloadIcon />
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};
